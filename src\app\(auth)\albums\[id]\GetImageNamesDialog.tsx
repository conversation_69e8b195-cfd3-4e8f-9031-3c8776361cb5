import { axiosInstance } from '@/lib/axios'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/lib/components/ui/dialog'
import { Label } from '@/lib/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/lib/components/ui/radio-group'
import { ImageName } from '@/lib/types'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { FC, useState } from 'react'

interface GetImageNamesDialogProps {
  albumId: string
  children?: React.ReactNode
}

const GetImageNamesDialog: FC<GetImageNamesDialogProps> = ({ albumId, children }) => {
  const [isFirstOpen, setIsFirstOpen] = useState(false)
  const { data } = useQuery({
    queryKey: [albumId, isFirstOpen],
    placeholderData: keepPreviousData,
    initialData: [],
    queryFn: async ({ signal }) => {
      const res = await axiosInstance.get(`/customer/album/image/get_og_filename/${albumId}`, { signal })
      if (res.status === 204) return []

      return res.data.customer_image as ImageName[]
    },
  })
  return (
    <Dialog
      onOpenChange={(open) => {
        if (open && !isFirstOpen) {
          setIsFirstOpen(true)
        }
      }}
    >
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-xl focus-visible:outline-0">
        <DialogHeader>
          <DialogTitle>Lấy danh sách tên file</DialogTitle>
          <DialogDescription className="hidden"></DialogDescription>
        </DialogHeader>
        <div className="flex gap-2 items-start">
          <RadioGroup defaultValue="txt">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <RadioGroupItem value="txt" id="txt" />
                <Label htmlFor="txt">.txt</Label>
              </div>
              <div className="flex items-center gap-3 pl-4">
                <RadioGroupItem value="txt-comma" id="txt-comma" />
                <Label htmlFor="txt-comma">Cách tên file bởi dấu phẩy</Label>
              </div>
              <div className="flex items-center gap-3 pl-4">
                <RadioGroupItem value="txt-lf" id="txt-lf" />
                <Label htmlFor="txt-lf">Các tên file bởi dòng</Label>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <RadioGroupItem value="json" id="json" />
              <Label htmlFor="json">.json</Label>
            </div>
          </RadioGroup>
          <RadioGroup defaultValue="all">
            <div className="flex items-center gap-3">
              <RadioGroupItem value="all" id="all" />
              <Label htmlFor="all">Toàn bộ ảnh</Label>
            </div>
            <div className="flex items-center gap-3">
              <RadioGroupItem value="picked" id="picked" />
              <Label htmlFor="picked">Chỉ ảnh khách đã thả tym</Label>
            </div>
          </RadioGroup>
        </div>
        <div></div>
      </DialogContent>
    </Dialog>
  )
}

export default GetImageNamesDialog
