import { useRouter, useSearchParams } from 'next/navigation'
import { useCallback, useMemo } from 'react'
import { parseSearchParams } from '@/lib/utils'
import { albumFilterSchema, AlbumFilterSchemaType } from '@/lib/zod-schema/album-filter-schema'
import { keepPreviousData, useInfiniteQuery } from '@tanstack/react-query'
import { AlbumImageResponse, CustomerImage } from '@/lib/types'
import { axiosInstance } from '@/lib/axios'
import { startOfDay, subDays } from 'date-fns'

const albumFilterDefault = albumFilterSchema.safeParse({}).data
const defaultAlbumData = {
  data: [] as CustomerImage[],
  page: 1,
  totalPage: 1,
}

export function useImagesQuery(
  {
    albumId,
    enteredPasscode,
  }: {
    albumId?: string
    enteredPasscode?: string
  },
  deps: unknown[] = [],
) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const queryParams = useMemo(() => {
    const searchParamsParsed = parseSearchParams(searchParams)
    return albumFilterSchema.safeParse(searchParamsParsed).data
  }, [searchParams])

  const onFilterChange: (filter: AlbumFilterSchemaType) => void = useCallback(
    (filter) => {
      const params = new URLSearchParams()
      Object.entries(filter).forEach(([key, value]) => {
        if (value === undefined || albumFilterDefault?.[key as keyof typeof albumFilterDefault] === value) {
          params.delete(key)
        } else {
          params.set(key, String(value))
        }
      })
      router.replace(`?${params}`)
    },
    [router],
  )

  const query = useInfiniteQuery<AlbumImageResponse>({
    queryKey: [...deps, albumId, queryParams?.privacy === 'ticked', queryParams],
    retry: false,
    placeholderData: keepPreviousData,
    getNextPageParam: (lastPage) => (lastPage.page < lastPage.totalPage ? lastPage.page + 1 : undefined),
    getPreviousPageParam: (firstPage) => (firstPage.page > 1 ? firstPage.page - 1 : undefined),
    initialPageParam: 1,
    queryFn: async ({ pageParam, signal, client }) => {
      client.removeQueries({
        queryKey: [...deps, albumId, queryParams?.privacy !== 'ticked', queryParams],
      })

      let result = defaultAlbumData
      if (albumId) {
        const params = new URLSearchParams()
        params.set('orderby', queryParams?.sort === 'oldest' ? 'asc' : 'desc')
        params.set('page', String(pageParam))
        params.set('is_ticked', queryParams?.privacy === 'ticked' ? '1' : '0')
        params.set('is_private', queryParams?.privacy === 'private' ? '1' : '0')

        switch (queryParams?.time) {
          case 'last7':
            params.set('start_date', startOfDay(subDays(new Date(), 7)).getTime().toString())
            break
          case 'last30':
            params.set('start_date', startOfDay(subDays(new Date(), 30)).getTime().toString())
            break
          case 'last180':
            params.set('start_date', startOfDay(subDays(new Date(), 180)).getTime().toString())
            break
        }

        if (queryParams?.time) {
          params.set('end_date', new Date().getTime().toString())
        }

        const res = await axiosInstance.get(`/customer/album/image/${albumId}?${params}`, {
          headers: {
            passcode: enteredPasscode || undefined,
          },
          signal,
        })

        if (res.status === 204) {
          return result
        }

        result = {
          data: (res.data.customer_image || []) as CustomerImage[],
          page: res.data.stats.current_page as number,
          totalPage: res.data.stats.total_page as number,
        }
      }

      return result
    },
  })

  return {
    ...query,
    queryParams,
    onFilterChange,
  }
}
