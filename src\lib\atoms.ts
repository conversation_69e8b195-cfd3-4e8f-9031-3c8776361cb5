import { atom, createStore } from 'jotai'
import { atomEffect } from 'jotai-effect'
import { screenSizeBreakpoints } from '@/lib/constants'
import { CustomerImage, ImageTag, UploadingImage } from '@/lib/types'
import { parseJwt } from '@/lib/utils'
import { ReactNode } from 'react'

export const store = createStore()

export const isMobileSidebarOpenAtom = atom(false)

const tokenPayloadAtom = atom(parseJwt(globalThis.localStorage?.getItem('token') ?? null))

export const userAtom = atom(
  (get) => get(tokenPayloadAtom),
  (_, set, newToken: string | null) => {
    if (newToken) {
      localStorage.setItem('token', newToken)
      set(tokenPayloadAtom, parseJwt(newToken))
    } else {
      localStorage.removeItem('token')
      set(tokenPayloadAtom, null)
    }
  },
)

export const screenSizeAtom = atom(screenSizeBreakpoints.base)

export const inSelectModeAtom = atom(false)

export const selectedAlbumImageIdsAtom = atom<number[]>([])

export const selectModeEffect = atomEffect((get, set) => {
  if (get(inSelectModeAtom) && get(selectedAlbumImageIdsAtom).length === 0) {
    set(inSelectModeAtom, false)
  }
})

export const uploadingImagesAtom = atom<Record<string, UploadingImage[]>>({})
export const syncingImagesAtom = atom<
  Record<
    string,
    Record<
      string,
      {
        handle: FileSystemDirectoryHandle
        images: UploadingImage[]
        observer?: FileSystemObserver
      }
    >
  >
>({})

export const lastUpdatedAtom = atom<Record<string, number>>({})

export const isQRCodeOpenAtom = atom(false)

export const rejectSetPasscodeAlbumIdsAtom = atom<string[]>([])

export const albumPasscodeAtom = atom<Record<string, string>>({})

export const headerItemAtom = atom<ReactNode | null>(null)

export const isTagDialogOpenAtom = atom(false)
export const editTagAtom = atom<ImageTag | null>(null)

export const addingTagImagesAtom = atom<{ albumId: string; images: CustomerImage[] } | null>(null)

export const viewingImageAtom = atom<CustomerImage | null>({
  id: 498,
  imageName: '6868e292310f3_GFX50S+II-6465.jpg.webp',
  uploaded_at: '2025-07-05T15:30:10+07:00',
  is_ticked: false,
  image_width: 1500,
  image_height: 1200,
  imageOptimized: {
    imageNameOptimized: '6868e292324ff_GFX50S+II-6465.jpg.webp',
    uploaded_at: '2025-07-05T15:30:10+07:00',
    image_width: 559,
    image_height: 447,
  },
  is_public: false,
  fullPath: '',
  imageTags: [],
})
