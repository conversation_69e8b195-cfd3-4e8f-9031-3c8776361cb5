'use client'

import { Avatar, AvatarImage } from '@/lib/components/ui/avatar'
import { Button, buttonVariants } from '@/lib/components/ui/button'
import { Separator } from '@/lib/components/ui/separator'
import { useAtom } from 'jotai'
import { Icon, IconifyIcon } from '@iconify/react'
import { FC, HTMLAttributes, useMemo, useRef } from 'react'
import { isMobileSidebarOpenAtom, userAtom } from '@/lib/atoms'
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/lib/components/ui/sheet'
import SwitchThemeButton from '@/lib/components/SwitchThemeButton'
import Link from 'next/link'
import { toast } from 'sonner'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import searchIcon from '@iconify-icons/material-symbols/search'
import addIcon from '@iconify-icons/material-symbols/add-rounded'
import tagIcon from '@iconify-icons/mdi/tag'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/lib/components/ui/dropdown-menu'
import AlbumInfoDialog from '@/lib/components/AlbumInfoDialog'

interface SideBarItem extends Omit<HTMLAttributes<HTMLElement>, 'className'> {
  id: string
  icon: IconifyIcon | string
  label: string
  href?: string
  hidden: boolean
}

const SidebarLeft: FC = () => {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useAtom(isMobileSidebarOpenAtom)
  const [user, setUser] = useAtom(userAtom)
  const albumInfoDialogTriggerEl = useRef<HTMLButtonElement>(null)
  const router = useRouter()
  const pathName = usePathname()
  const searchParams = useSearchParams()
  const items: SideBarItem[] = useMemo(
    () => [
      {
        id: 'home',
        icon: 'fa6-solid:house',
        label: 'Trang chủ',
        href: '/',
        hidden: false,
      },
      {
        id: 'albums',
        icon: 'ion:images-sharp',
        label: 'Quản lý album',
        href: '/albums',
        hidden: false,
      },
      {
        id: 'tags',
        icon: tagIcon,
        label: 'Quản lý tag',
        href: '/tags',
        hidden: false,
      },
      {
        id: 'add-new',
        icon: addIcon,
        label: 'Thêm mới',
        hidden: !user,
        onClick: () => albumInfoDialogTriggerEl.current?.click(),
      },
    ],
    [user],
  )

  function handleLogout() {
    router.push('/login')
    setIsMobileSidebarOpen(false)
    setUser(null)
    toast.success('Đăng xuất thành công')
  }

  return (
    <>
      {user && (
        <AlbumInfoDialog onSuccess={() => router.push('/albums')}>
          <button type="button" ref={albumInfoDialogTriggerEl} className="hidden"></button>
        </AlbumInfoDialog>
      )}
      <div className="fixed top-0 left-0 bottom-0 w-18 bg-background hidden sm:flex flex-col items-center justify-between item py-4 border-r">
        <div className="flex flex-col items-center gap-4">
          <Button className="size-10" variant="default">
            <Icon fontSize={4} icon={searchIcon} className="size-6" />
          </Button>
          <Separator className="!w-10" />
          {items
            .filter((x) => !x.hidden)
            .map(({ id, href, icon, label, ...others }) =>
              href ? (
                <Link key={id} href={href} className="flex flex-col items-center group gap-0.5">
                  <Button
                    variant={
                      (href === '/' && pathName === href) || (href !== '/' && pathName.startsWith(href))
                        ? 'default'
                        : 'ghost'
                    }
                    className="size-10 p-0"
                    {...others}
                  >
                    <Icon icon={icon} className="size-5" />
                  </Button>
                  <span className="text-xs tracking-tighter text-center">{label}</span>
                </Link>
              ) : (
                <div key={id} className="flex flex-col items-center group gap-0.5">
                  <Button variant="ghost" className="size-10 p-0" {...others}>
                    <Icon icon={icon} className="size-7" />
                  </Button>
                  <span className="text-xs tracking-tighter text-center">{label}</span>
                </div>
              ),
            )}
        </div>
        <div className="flex flex-col items-center gap-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Avatar className="size-10 cursor-pointer hover:brightness-75 transition-all">
                <AvatarImage src="/images/default-avatar.jpg" alt="user" />
              </Avatar>
            </DropdownMenuTrigger>
            <DropdownMenuContent side="top" align="start" alignOffset={32}>
              <DropdownMenuLabel className="font-semibold">Tài khoản</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {user ? (
                <DropdownMenuItem className="cursor-pointer" onClick={handleLogout}>
                  Đăng xuất
                </DropdownMenuItem>
              ) : (
                <>
                  <Link href={`/login?redirect=${pathName}${searchParams.size > 0 ? `?${searchParams}` : ''}`}>
                    <DropdownMenuItem className="cursor-pointer">Đăng nhập</DropdownMenuItem>
                  </Link>
                  <Link href="/register">
                    <DropdownMenuItem className="cursor-pointer">Đăng ký</DropdownMenuItem>
                  </Link>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
          <SwitchThemeButton />
        </div>
      </div>
      <Sheet
        open={isMobileSidebarOpen}
        onOpenChange={(open) => {
          if (!open) {
            setIsMobileSidebarOpen(false)
          }
        }}
      >
        <SheetContent side="left" className="px-4">
          <SheetHeader>
            <SheetTitle></SheetTitle>
          </SheetHeader>
          {items
            .filter((x) => !x.hidden)
            .map(({ id, href, icon, label, ...others }) => (
              <div key={id} onClick={() => setIsMobileSidebarOpen(false)}>
                {href ? (
                  <Link
                    key={id}
                    href={href}
                    className={buttonVariants({
                      variant:
                        (href === '/' && pathName === href) || (href !== '/' && pathName.startsWith(href))
                          ? 'secondary'
                          : 'ghost',
                      className: 'w-full h-10 justify-start active:bg-secondary',
                    })}
                    {...others}
                  >
                    <Icon icon={icon} className="size-6 px-1" />
                    <span>{label}</span>
                  </Link>
                ) : (
                  <Button
                    key={id}
                    variant="ghost"
                    className="w-full h-10 justify-start active:bg-secondary"
                    {...others}
                  >
                    <Icon icon={icon} className="size-6" />
                    <span>{label}</span>
                  </Button>
                )}
              </div>
            ))}
        </SheetContent>
      </Sheet>
    </>
  )
}

export default SidebarLeft
