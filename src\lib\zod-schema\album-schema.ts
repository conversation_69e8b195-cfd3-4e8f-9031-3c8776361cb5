import { z } from 'zod'

export const createAlbumSchema = z.object({
  name: z.string().min(1, { message: 'Tên khách hàng không được để trống' }),
  limit: z.coerce.number().min(1, { message: '<PERSON><PERSON> lượng ảnh không được để trống' }),
  endDate: z.date().min(new Date(), { message: 'Deadline khách chọn ảnh không được để trống' }),
})

export type CreateAlbumSchemaType = z.infer<typeof createAlbumSchema>
