export interface ImageTag {
  color: string
  id: number
  name: string
  created_at: string
  last_updated_at: string
}

export interface CustomerImage {
  id: number
  imageName: string
  uploaded_at: string
  is_ticked: boolean
  image_width: number
  image_height: number
  imageOptimized: {
    imageNameOptimized: string
    uploaded_at: string
    image_width: number
    image_height: number
  }
  is_public: boolean
  fullPath?: string
  imageTags: ImageTag[]
}

export interface ImageName {
  id: number
  imageName: string
  og_filename: string
}

export interface Album {
  code: string
  created_at: string
  last_updated_at: string
  customer_name: string
  limit_image: number
  is_public: boolean
  is_public_name: boolean
  is_passcode_protected: boolean
  is_customer_auth: boolean
  end_date: string
  ptg: string
  thumbnail: Pick<CustomerImage, 'imageOptimized'>[]
}

export type UploadingImageState =
  | 'pending'
  | 'loading'
  | 'compressing'
  | 'uploading'
  | 'uploaded'
  | 'failed'
  | 'deleted'

export interface UploadingImage {
  id: string
  file: File
  fileName: string
  state: UploadingImageState
  failedReason?: string
  localUrl?: string
  process: () => void
  controller: AbortController
  uploadedAt?: string
  remoteImageUrl?: string
  remoteThumbnailUrl?: string
  remoteThumbnailFallbackUrl?: string
  originalWidth?: number
  originalHeight?: number
  convertedWidth?: number
  convertedHeight?: number
  thumbnailWidth?: number
  thumbnailHeight?: number
}

export interface TokenPayload {
  iat: number
  exp: number
  roles: string[]
  username: string
}

export interface AlbumImageResponse {
  data: CustomerImage[]
  page: number
  totalPage: number
}
