'use client'

import { FC, ReactNode, useEffect, useMemo, useRef } from 'react'
import { CustomerImage } from '@/lib/types'
import { useAtomValue } from 'jotai'
import { screenSizeAtom } from '@/lib/atoms'
import { screenSizeBreakpoints } from '@/lib/constants'
import { Loader2 } from 'lucide-react'
import { AlbumFilterSchemaType } from '@/lib/zod-schema/album-filter-schema'
import FilterSelect, { FilterSelectOption } from '@/lib/components/FilterSelect'

interface AlbumMasonryProps {
  data: CustomerImage[]
  children: (imageData: CustomerImage) => React.ReactNode
  onScrollBottom?: () => void
  isFetching?: boolean
  sort?: AlbumFilterSchemaType['sort']
  privacy?: AlbumFilterSchemaType['privacy']
  time?: AlbumFilterSchemaType['time']
  isAuth?: boolean
  onFilterChange?: (filter: AlbumFilterSchemaType) => void
  toolbar?: ReactNode
}

const sortFilterOptions: FilterSelectOption<AlbumFilterSchemaType['sort']>[] = [
  { value: 'newest', label: 'Mới nhất' },
  { value: 'oldest', label: 'Cũ nhất' },
]
const privacyFilterOptions: FilterSelectOption<AlbumFilterSchemaType['privacy']>[] = [
  { value: 'all', label: 'Toàn bộ ảnh' },
  { value: 'private', label: 'Chỉ ảnh riêng tư' },
  { value: 'ticked', label: 'Chỉ ảnh đã thả tym' },
]
const timeFilterOptions: FilterSelectOption<NonNullable<AlbumFilterSchemaType['time']>>[] = [
  { value: 'last7', label: '7 ngày gần nhất' },
  { value: 'last30', label: '30 ngày gần nhất' },
  { value: 'last180', label: '180 ngày gần nhất' },
  { value: 'custom', label: 'Thời gian tuỳ chọn' },
]

const AlbumMasonry: FC<AlbumMasonryProps> = ({
  data,
  children,
  onScrollBottom,
  isFetching,
  sort = 'newest',
  privacy = 'all',
  time,
  onFilterChange,
  toolbar,
  isAuth = false,
}) => {
  const bottomElementRef = useRef<HTMLDivElement>(null)
  const screenSize = useAtomValue(screenSizeAtom)
  const columnNumber = useMemo(() => {
    if (screenSize >= screenSizeBreakpoints.xl) return 4
    if (screenSize >= screenSizeBreakpoints.md) return 3
    return 2
  }, [screenSize])
  const imageColumns = useMemo(() => {
    const columnHeights = Array(columnNumber).fill(0)
    const imageColumns: CustomerImage[][] = Array.from({ length: columnNumber }, () => [])

    data.forEach((x) => {
      const minHeightColumn = columnHeights.indexOf(Math.min(...columnHeights))
      imageColumns[minHeightColumn].push(x)
      columnHeights[minHeightColumn] += x.image_height
    })

    return imageColumns
  }, [data, columnNumber])

  useEffect(() => {
    if (!bottomElementRef.current) return

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          onScrollBottom?.()
        }
      })
    })
    observer.observe(bottomElementRef.current)

    return () => {
      observer.disconnect()
    }
  }, [onScrollBottom])

  return (
    <>
      <div className="flex justify-between gap-2">
        <div className="flex gap-2">
          <FilterSelect
            value={sort}
            defaultValue="newest"
            onChange={(value) => onFilterChange?.({ sort: value as AlbumFilterSchemaType['sort'], privacy, time })}
            options={sortFilterOptions}
          />
          {isAuth && (
            <FilterSelect
              value={privacy}
              defaultValue="all"
              onChange={(value) => onFilterChange?.({ sort, privacy: value as AlbumFilterSchemaType['privacy'], time })}
              options={privacyFilterOptions}
            />
          )}
          <FilterSelect
            value={time}
            onChange={(value) => onFilterChange?.({ sort, privacy, time: value as AlbumFilterSchemaType['time'] })}
            options={timeFilterOptions}
            placeholder="Chọn thời gian"
          />
        </div>
        <div>{toolbar}</div>
      </div>
      <div
        style={{
          gridTemplateColumns: `repeat(${columnNumber}, 1fr)`,
        }}
        className="grid gap-responsive relative"
      >
        {imageColumns.map((images, i) => (
          <div key={i} className="flex flex-col gap-responsive">
            {images.map((x) => (
              <div
                key={x.id}
                style={{
                  aspectRatio: `${x.image_width}/${x.image_height}`,
                }}
                className="relative"
              >
                {children(x)}
              </div>
            ))}
          </div>
        ))}
      </div>
      {isFetching && (
        <div className="flex items-center justify-center pt-4">
          <Loader2 className="size-16 animate-spin" />
        </div>
      )}
      <div
        ref={bottomElementRef}
        className="absolute bottom-0 left-0 right-0 pointer-events-none h-[1000px] max-h-full"
      ></div>
    </>
  )
}

export default AlbumMasonry
