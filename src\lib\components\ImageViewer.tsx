'use client'

import { FC, useRef, useState, useCallback, useEffect } from 'react'
import { <PERSON><PERSON>, DialogHeader, DialogOverlay, DialogPortal, DialogTitle } from '@/lib/components/ui/dialog'
import { Content } from '@radix-ui/react-dialog'
import './ImageViewer.css'
import { Button } from '@/lib/components/ui/button'
import { Brush, Hand, Search } from 'lucide-react'
import { useAtom } from 'jotai'
import { viewingImageAtom } from '@/lib/atoms'
import { imageBaseUrl } from '@/lib/constants'
import { getStroke } from 'perfect-freehand'
import { getSvgPathFromStroke } from '../utils'
import { Toggle } from '@radix-ui/react-toggle'
import { Separator } from './ui/separator'

type CursorMode = 'move' | 'draw' | 'comment'

const minZoom = 0.2
const maxZoom = 5

const ImageViewer: FC = () => {
  const [viewingImage, setViewingImage] = useAtom(viewingImageAtom)
  const [open, setOpen] = useState(!!viewingImage)
  const [viewingImageLocal, setViewingImageLocal] = useState(viewingImage)
  const imageRef = useRef<HTMLImageElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const zoomContainerRef = useRef<HTMLDivElement>(null)
  const svgPathRef = useRef<SVGPathElement>(null)
  const zoomRef = useRef(1)
  const positionRef = useRef({ x: 0, y: 0 })
  const isDraggingRef = useRef(false)
  // const isDrawingRef = useRef(false)
  const dragStartRef = useRef({ x: 0, y: 0 })
  // const isPanningAllowedRef = useRef(false)
  // const drawingPointsRef = useRef<{ x: number; y: number }[]>([])
  // const [cursorMode, setCursorMode] = useState<CursorMode>('move')

  // const updateTransform = useCallback(() => {
  //   const panContainer = panContainerRef.current
  //   const zoomContainer = zoomContainerRef.current
  //   if (!panContainer || !zoomContainer) return

  //   // Pan container handles translation (position)
  //   panContainer.style.transform = `translate(${positionRef.current.x}px, ${positionRef.current.y}px)`
  //   // Zoom container handles scaling
  //   zoomContainer.style.transform = `scale(${zoomRef.current})`
  // }, [])

  const resetZoomAndPan = () => {
    contentRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
  }

  // const handleWheel = useCallback(
  //   (e: WheelEvent) => {
  //     e.preventDefault()

  //     const zoomContainer = zoomContainerRef.current
  //     if (!zoomContainer) return

  //     const imageRect = zoomContainer.getBoundingClientRect()

  //     // Calculate mouse position relative to the zoom container
  //     const mouseX = e.clientX - imageRect.left
  //     const mouseY = e.clientY - imageRect.top

  //     const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1
  //     const currentZoom = zoomRef.current
  //     const newZoom = Math.min(Math.max(currentZoom * zoomFactor, minZoom), maxZoom)

  //     if (newZoom !== currentZoom) {
  //       // Calculate new position to zoom into mouse position
  //       const zoomRatio = newZoom / currentZoom
  //       const newX = positionRef.current.x + mouseX * (1 - zoomRatio)
  //       const newY = positionRef.current.y + mouseY * (1 - zoomRatio)

  //       zoomRef.current = newZoom
  //       positionRef.current = { x: newX, y: newY }
  //       updateTransform()
  //     }
  //   },
  //   [updateTransform],
  // )

  // const contentRef = useCallback(
  //   (node: HTMLDivElement | null) => {
  //     if (node) {
  //       node.addEventListener('wheel', handleWheel, { passive: false })
  //       return () => {
  //         node.removeEventListener('wheel', handleWheel)
  //       }
  //     }
  //   },
  //   [handleWheel],
  // )

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    isDraggingRef.current = true
    positionRef.current = {
      x: e.clientX,
      y: e.clientY,
    }
  }, [])

  const handleWheel = useCallback(
    (e: React.WheelEvent) => {
      const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1
      const currentZoom = zoomRef.current
      const newZoom = Math.min(Math.max(currentZoom * zoomFactor, minZoom), maxZoom)

      zoomRef.current = newZoom
      if (imageRef.current && zoomContainerRef.current && viewingImageLocal) {
        imageRef.current.width = viewingImageLocal.image_width * newZoom
        imageRef.current.height = viewingImageLocal.image_height * newZoom
        zoomContainerRef.current.style.width = `calc((100vw - 2rem) * ${newZoom})`
        zoomContainerRef.current.style.height = `calc((100vh - 10rem) * ${newZoom})`
      }
    },
    [viewingImageLocal],
  )

  // // Initialize transform on mount
  // useEffect(() => {
  //   updateTransform()
  // }, [updateTransform])

  // // Track Ctrl key state and prevent page zoom
  // useEffect(() => {
  //   const handleKeyDown = (e: KeyboardEvent) => {
  //     if (e.key === 'Control') {
  //       isPanningAllowedRef.current = true
  //       if (zoomContainerRef.current) {
  //         zoomContainerRef.current.style.cursor = 'grab'
  //       }
  //     }
  //     if (e.ctrlKey && (e.key === '=' || e.key === '-' || e.key === '0')) {
  //       e.preventDefault()
  //     }
  //   }

  //   const handleKeyUp = (e: KeyboardEvent) => {
  //     if (e.key === 'Control') {
  //       isPanningAllowedRef.current = false
  //       isDraggingRef.current = false
  //       if (zoomContainerRef.current) {
  //         zoomContainerRef.current.style.cursor = ''
  //       }
  //     }
  //   }

  //   const handleWheel = (e: WheelEvent) => {
  //     if (e.ctrlKey) {
  //       e.preventDefault()
  //     }
  //   }

  //   document.addEventListener('keydown', handleKeyDown)
  //   document.addEventListener('keyup', handleKeyUp)
  //   document.addEventListener('wheel', handleWheel, { passive: false })

  //   return () => {
  //     document.removeEventListener('keydown', handleKeyDown)
  //     document.removeEventListener('keyup', handleKeyUp)
  //     document.removeEventListener('wheel', handleWheel)
  //   }
  // }, [])

  useEffect(() => {
    if (!viewingImageLocal) return

    // const handleWheel = (e: WheelEvent) => {
    //   e.preventDefault()
    //   const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1
    //   const currentZoom = zoomRef.current
    //   const newZoom = Math.min(Math.max(currentZoom * zoomFactor, minZoom), maxZoom)

    //   zoomRef.current = newZoom
    //   if (imageRef.current && zoomContainerRef.current) {
    //     imageRef.current.width = viewingImageLocal!.image_width * newZoom
    //     imageRef.current.height = viewingImageLocal!.image_height * newZoom
    //     zoomContainerRef.current.style.width = `calc((100vw - 2rem) * ${newZoom})`
    //     zoomContainerRef.current.style.height = `calc((100vh - 10rem) * ${newZoom})`
    //   }
    // }
    // const handleMouseDown = (e: MouseEvent) => {
    //   isDraggingRef.current = true
    //   positionRef.current = {
    //     x: e.clientX,
    //     y: e.clientY,
    //   }
    // }
    const handleMouseMove = (e: MouseEvent) => {
      if (isDraggingRef.current && contentRef.current) {
        contentRef.current.scrollLeft -= e.clientX - positionRef.current.x
        contentRef.current.scrollTop -= e.clientY - positionRef.current.y
      }
      positionRef.current = {
        x: e.clientX,
        y: e.clientY,
      }
    }
    const handleMouseUp = () => {
      isDraggingRef.current = false
    }

    // document.addEventListener('wheel', handleWheel, { passive: false })
    // document.addEventListener('mousedown', handleMouseDown)
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.addEventListener('mouseleave', handleMouseUp)

    return () => {
      // document.removeEventListener('wheel', handleWheel)
      // document.removeEventListener('mousedown', handleMouseDown)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('mouseleave', handleMouseUp)
    }
  }, [viewingImageLocal])

  useEffect(() => {
    if (viewingImage) {
      setViewingImageLocal(viewingImage)
      setOpen(true)
    } else {
      setOpen(false)
    }
  }, [viewingImage])

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          setViewingImage(null)
        }
      }}
    >
      <DialogPortal>
        <DialogOverlay />
        <Content
          ref={contentRef}
          aria-describedby={undefined}
          className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-0 left-0 bottom-0 right-0 z-50 duration-200 focus-visible:outline-0 inset-0 overflow-hidden select-text"
        >
          <DialogHeader className="hidden">
            <DialogTitle></DialogTitle>
          </DialogHeader>
          <div className="absolute left-0 top-0 grid-cols-[70vw_auto_70vw] grid-rows-[70vh_auto_70vh] grid size-max">
            <div
              ref={zoomContainerRef}
              className="col-start-2 row-start-2 flex justify-center items-center w-[calc(100vw-2rem)] h-[calc(100vh-10rem)]"
              style={{ aspectRatio: viewingImageLocal!.image_width / viewingImageLocal!.image_height }}
            >
              <img
                onMouseDown={handleMouseDown}
                onWheel={handleWheel}
                ref={imageRef}
                src={viewingImageLocal?.fullPath || `${imageBaseUrl}${viewingImageLocal?.imageName}`}
                alt={viewingImageLocal?.imageName}
                width={viewingImageLocal?.image_width}
                height={viewingImageLocal?.image_height}
                className="max-w-full max-h-full object-contain block relative"
                draggable={false}
              />
            </div>
          </div>
          {/* <div ref={panContainerRef} className="inline-block relative origin-top-left">
            <div
              ref={zoomContainerRef}
              className="bg-white inline-block relative origin-top-left"
              style={{ cursor: 'default', pointerEvents: 'initial' }}
            >
              <div className="checkered absolute inset-0 bg-white"></div>
              <img
                ref={imageRef}
                src={viewingImageLocal?.fullPath || `${imageBaseUrl}${viewingImageLocal?.imageName}`}
                alt=""
                className="max-w-[calc(100vw-2rem)] max-h-[calc(100vh-10rem)] object-contain block relative -m-[1px]"
                draggable={false}
              />
              <svg className="absolute top-0 left-0 size-full" style={{ touchAction: 'none' }}>
                <path ref={svgPathRef} />
              </svg>
            </div>
          </div> */}
        </Content>
      </DialogPortal>
    </Dialog>
  )
}

export default ImageViewer
