@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  --background: #ffffff;
  --foreground: #252525;
  --card: #ffdbf7;
  --card-foreground: #24001b;
  --popover: #ffffff;
  --popover-foreground: #252525;
  --primary: #7f0660;
  --primary-foreground: #fbfbfb;
  --secondary: #ffdbf7;
  --secondary-foreground: #000000;
  --muted: #fef6fe;
  --muted-foreground: #000000;
  --accent: #e8b0da;
  --accent-foreground: #7f0660;
  --destructive: #e93d3d;
  --destructive-foreground: #ffffff;
  --border: #ffdbf7;
  --input: #e8b0da;
  --ring: #b5b5b5;
  --chart-1: #ebbf5e;
  --chart-2: #54d3de;
  --chart-3: #2440a7;
  --chart-4: #d9efb8;
  --chart-5: #dfec9c;
  --sidebar: #fbfbfb;
  --sidebar-foreground: #252525;
  --sidebar-primary: #343434;
  --sidebar-primary-foreground: #fbfbfb;
  --sidebar-accent: #f7f7f7;
  --sidebar-accent-foreground: #343434;
  --sidebar-border: #ebebeb;
  --sidebar-ring: #b5b5b5;
  --font-sans: Roboto, sans-serif;
  --font-serif: Roboto, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --radius: 0.675rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

.dark {
  --background: #09090b;
  --foreground: #fef6fc;
  --card: #261121;
  --card-foreground: #ffffff;
  --popover: #09090b;
  --popover-foreground: #fafafa;
  --primary: #f97fdb;
  --primary-foreground: #18181b;
  --secondary: #261121;
  --secondary-foreground: #f97fdb;
  --muted: #281121;
  --muted-foreground: #fde7f1;
  --accent: #36192f;
  --accent-foreground: #f97fdb;
  --destructive: #e93d3d;
  --destructive-foreground: #fef2f2;
  --border: #261121;
  --input: #36192f;
  --ring: #ffc7c7;
  --chart-1: #2662d9;
  --chart-2: #2eb88a;
  --chart-3: #e88c30;
  --chart-4: #af57db;
  --chart-5: #e23670;
  --sidebar: #18181b;
  --sidebar-foreground: #f4f4f5;
  --sidebar-primary: #1d4ed8;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #27272a;
  --sidebar-accent-foreground: #f4f4f5;
  --sidebar-border: #27272a;
  --sidebar-ring: #d4d4d8;
  --font-sans: Roboto, sans-serif;
  --font-serif: Roboto, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --radius: 0.675rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-inter);
  --font-sans--font-feature-settings: 'cv01', 'cv02', 'cv03', 'cv04', 'cv08', 'cv11', 'tnum';
  --font-mono: var(--font-jetbrains-mono);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@theme {
  --spacing-responsive: 8px;
  --spacing-body: calc(100dvh - 56px);
}

@layer theme {
  @media (width >= 48rem /* 768px */) {
    :root {
      --spacing-responsive: 16px;
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground font-sans text-sm sm:text-base;
  }

  [role='button'],
  button {
    cursor: pointer;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}
