'use client'

import { FC, useMemo, useState } from 'react'
import { HexColorPicker } from 'react-colorful'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/lib/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/lib/components/ui/popover'
import { Input } from '@/lib/components/ui/input'

interface ColorPickerProps {
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
}

const ColorPicker: FC<Omit<React.ComponentProps<'button'>, 'value' | 'onChange' | 'onBlur'> & ColorPickerProps> = ({
  disabled,
  value,
  onChange,
  onBlur,
  name,
  className,
  ...props
}) => {
  const [open, setOpen] = useState(false)
  const parsedValue = useMemo(() => {
    return value || '#FFFFFF'
  }, [value])

  return (
    <Popover onOpenChange={setOpen} open={open}>
      <PopoverTrigger asChild disabled={disabled} onBlur={onBlur}>
        <Button
          {...props}
          className={cn('block size-9', className)}
          name={name}
          onClick={() => {
            setOpen(true)
          }}
          style={{
            backgroundColor: parsedValue,
          }}
          variant="outline"
        >
          <div />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full space-y-4">
        <HexColorPicker color={parsedValue} onChange={onChange} />
        <Input
          maxLength={7}
          onChange={(e) => {
            onChange(e?.currentTarget?.value)
          }}
          className="w-50"
          value={parsedValue}
        />
      </PopoverContent>
    </Popover>
  )
}
ColorPicker.displayName = 'ColorPicker'

export { ColorPicker }
