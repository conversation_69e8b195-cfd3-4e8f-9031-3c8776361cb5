import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { TokenPayload } from './types'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function parseJwt(token: string | null) {
  if (!token) return null
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      globalThis
        .atob(base64)
        .split('')
        .map((c) => {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
        })
        .join(''),
    )
    return JSON.parse(jsonPayload) as TokenPayload
  } catch (error) {
    console.error(error)
    return null
  }
}

export const uuidv4 = () => {
  let d = new Date().getTime()
  let d2 = (typeof performance !== 'undefined' && performance.now && performance.now() * 1000) || 0 //Time in microseconds since page-load or 0 if unsupported
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16 //random number between 0 and 16
    if (d > 0) {
      //Use timestamp until depleted
      r = (d + r) % 16 | 0
      d = Math.floor(d / 16)
    } else {
      //Use microseconds since page-load if supported
      r = (d2 + r) % 16 | 0
      d2 = Math.floor(d2 / 16)
    }
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16)
  })
}

export function parseSearchParams(searchParams: URLSearchParams) {
  const keyGrouped = [...searchParams.entries()].reduce(
    (prev, cur) => ((prev[cur[0]] || (prev[cur[0]] = [])).push(cur), prev),
    {} as Record<string, [string, string][]>,
  )
  return Object.fromEntries(
    Object.values(keyGrouped)
      .filter((x) => x?.length)
      .map((x) => {
        if (x!.length === 1) {
          return x![0]
        }
        return [x![0][0], x!.map((x) => x[1])]
      }),
  ) as Record<string, string | string[]>
}

function med(A: number[], B: number[]) {
  return [(A[0] + B[0]) / 2, (A[1] + B[1]) / 2]
}

const TO_FIXED_PRECISION = /(\s?[A-Z]?,?-?[0-9]*\.[0-9]{0,2})(([0-9]|e|-)*)/g

export function getSvgPathFromStroke(points: number[][]): string {
  if (!points.length) {
    return ''
  }

  const max = points.length - 1

  return points
    .reduce(
      (acc, point, i, arr) => {
        if (i === max) {
          acc.push(point, med(point, arr[0]), 'L', arr[0], 'Z')
        } else {
          acc.push(point, med(point, arr[i + 1]))
        }
        return acc
      },
      ['M', points[0], 'Q'],
    )
    .join(' ')
    .replace(TO_FIXED_PRECISION, '$1')
}
