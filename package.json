{"name": "lalacos", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 4041", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@jsquash/webp": "^1.5.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.76.2", "@tanstack/react-table": "^8.21.3", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.9.0", "babel-plugin-react-compiler": "^19.1.0-rc.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "immer": "^10.1.1", "jotai": "^2.12.4", "jotai-effect": "^2.0.2", "jotai-immer": "^0.4.1", "lucide-react": "^0.511.0", "next": "15.3.2", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "p-queue": "^8.1.0", "perfect-freehand": "^1.2.2", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "sonner": "^2.0.4", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2", "zod": "^3.25.30"}, "devDependencies": {"@eslint/eslintrc": "^3", "@iconify-icons/material-symbols": "^1.2.58", "@iconify-icons/mdi": "^1.2.48", "@iconify/react": "^6.0.0", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/wicg-file-system-access": "^2023.10.6", "eslint": "^9", "eslint-config-next": "15.3.2", "prettier": "^3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}