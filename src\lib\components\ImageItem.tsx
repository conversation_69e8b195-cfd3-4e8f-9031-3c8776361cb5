import { Button } from '@/lib/components/ui/button'
import { FC, useCallback, useRef } from 'react'
import { Check, Pencil } from 'lucide-react'
import { CustomerImage } from '@/lib/types'
import { imageBaseUrl } from '@/lib/constants'
import globeIcon from '@iconify-icons/mdi/globe'
import globeOffIcon from '@iconify-icons/mdi/globe-off'
import heartIcon from '@iconify-icons/mdi/heart'
import { Icon } from '@iconify/react'
import './ImageItem.css'
import Tag from '@/lib/components/Tag'
import { useSetAtom } from 'jotai'
import { addingTagImagesAtom } from '@/lib/atoms'

interface ImageItemProps {
  imageData: CustomerImage
  albumId: string
  isSelected: boolean
  inSelectMode: boolean
  onSelectBtnClick?: () => void
  onWrapperLongPress?: () => void
  onWrapperClick?: () => void
  selectable?: boolean
  isShowBadge?: boolean
  isTagEditable?: boolean
}

const ImageItem: FC<ImageItemProps> = ({
  imageData,
  albumId,
  isSelected,
  inSelectMode,
  onSelectBtnClick,
  onWrapperLongPress,
  onWrapperClick,
  selectable = true,
  isShowBadge = true,
  isTagEditable = false,
}) => {
  const timer = useRef<NodeJS.Timeout | null>(null)
  const setAddingTagImages = useSetAtom(addingTagImagesAtom)

  const handleTouchStart = useCallback(() => {
    timer.current = setTimeout(() => {
      onWrapperLongPress?.()
    }, 500)
  }, [onWrapperLongPress])

  const handleTouchEnd = useCallback(() => {
    if (timer.current != null) {
      clearTimeout(timer.current)
    }
  }, [])

  const handleTouchMove = useCallback(() => {
    if (timer.current != null) {
      clearTimeout(timer.current)
    }
  }, [])

  return (
    <div
      className="size-full rounded-md overflow-hidden relative group transition-all outline-solid outline-neutral-200 dark:outline-neutral-800 -outline-offset-4 md:outline-offset-0"
      style={{
        padding: isSelected || inSelectMode ? 'calc((var(--spacing-responsive) / 2 * -1) + 12px)' : '0px',
        outlineWidth: isSelected || inSelectMode ? '4px' : '0px',
        outlineColor: isSelected ? 'var(--primary)' : undefined,
      }}
      onClick={selectable ? onWrapperClick : undefined}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onTouchMove={handleTouchMove}
    >
      <div className="size-full rounded-xs md:rounded-sm overflow-hidden relative bg-red-500/15">
        <img
          className="size-full object-cover absolute top-0 left-0 pointer-events-none"
          src={imageData.fullPath ?? `${imageBaseUrl}${imageData.imageOptimized.imageNameOptimized}`}
          alt={imageData.imageName}
        />
      </div>
      {selectable && (
        <Button
          variant={isSelected ? 'default' : 'secondary'}
          size="icon"
          style={{
            opacity: isSelected ? 1 : inSelectMode ? 0.3 : 0,
            outlineWidth: isSelected ? '2px' : '0px',
          }}
          className="absolute top-4 right-4 md:top-2 md:right-2 rounded-full group-hover:!opacity-100 transition-all outline-solid outline-secondary"
          onClick={onSelectBtnClick}
        >
          <Check className="size-6" />
        </Button>
      )}
      <div className="absolute pointer-events-none left-2 bottom-2 flex flex-col gap-2">
        {isShowBadge && (
          <div className="flex gap-2">
            <div className="pointer-events-auto flex justify-center items-center size-9 bg-secondary text-secondary-foreground rounded-full">
              <Icon icon={imageData.is_public ? globeIcon : globeOffIcon} className="size-5" />
            </div>
            {imageData.is_ticked && (
              <div className="pointer-events-auto flex justify-center items-center size-9 bg-secondary text-secondary-foreground rounded-full">
                <Icon icon={heartIcon} className="size-5" />
              </div>
            )}
          </div>
        )}
        <div className="flex gap-2 empty:hidden">
          {imageData.imageTags?.map((tag) => <Tag key={tag.id} tag={tag} />)}
          {isTagEditable && (
            <Button
              variant="secondary"
              className="size-6 pointer-events-auto p-0"
              onClick={() => {
                setAddingTagImages({
                  albumId: albumId,
                  images: [imageData],
                })
              }}
            >
              <Pencil strokeWidth={2.5} className="size-3" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export default ImageItem
